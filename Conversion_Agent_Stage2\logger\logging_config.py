"""
Stage2 Logging Configuration

Defines logging standards, conventions, and configuration for Stage2 workflow.
"""

from dataclasses import dataclass
from typing import Dict, List
import os


@dataclass
class LoggingConfig:
    """Configuration class for Stage2 logging standards."""
    
    # File naming conventions
    LOG_PREFIX: str = "stage2"
    TIMESTAMP_FORMAT: str = "%Y%m%d_%H%M%S"
    FILENAME_SEPARATOR: str = "_"
    FILE_EXTENSION: str = ".log"
    
    # Directory structure
    LOG_SUBDIRECTORY: str = "logs"
    BASE_METADATA_PATH: str = "Stage1_Metadata"
    
    # File encoding and formatting
    FILE_ENCODING: str = "utf-8"
    SECTION_SEPARATOR: str = "=" * 80
    SUBSECTION_PREFIX: str = "---"
    
    # Log retention (future enhancement)
    MAX_LOG_FILES: int = 50
    LOG_RETENTION_DAYS: int = 30


class LoggingStandards:
    """
    Defines logging standards and best practices for Stage2 workflow.
    """
    
    @staticmethod
    def get_standard_filename(
        migration_name: str,
        schema_name: str,
        object_name: str,
        object_type: str,
        timestamp: str
    ) -> str:
        """
        Generate standardized log filename.
        
        Format: stage2_YYYYMMDD_HHMMSS_migration_schema_objecttype_object.log
        
        Args:
            migration_name: Migration name
            schema_name: Schema name  
            object_name: Object name
            object_type: Object type
            timestamp: Formatted timestamp
            
        Returns:
            str: Standardized filename
        """
        config = LoggingConfig()
        
        # Sanitize names for filename compatibility
        migration_clean = LoggingStandards._sanitize_filename_part(migration_name)
        schema_clean = LoggingStandards._sanitize_filename_part(schema_name)
        object_clean = LoggingStandards._sanitize_filename_part(object_name)
        object_type_clean = LoggingStandards._sanitize_filename_part(object_type.lower())
        
        # Build filename components
        components = [
            config.LOG_PREFIX,
            timestamp,
            migration_clean,
            schema_clean,
            object_type_clean,
            object_clean
        ]
        
        # Join with separator and add extension
        filename = config.FILENAME_SEPARATOR.join(components) + config.FILE_EXTENSION
        
        return filename
    
    @staticmethod
    def get_standard_directory(
        qbook_path: str,
        migration_name: str,
        schema_name: str,
        object_name: str,
        object_type: str
    ) -> str:
        """
        Generate standardized log directory path.
        
        Format: {qbook_path}/Stage1_Metadata/{migration}/{schema}/{object_type}/{object}/logs/
        
        Args:
            qbook_path: Base QBook path
            migration_name: Migration name
            schema_name: Schema name
            object_name: Object name
            object_type: Object type
            
        Returns:
            str: Standardized directory path
        """
        config = LoggingConfig()
        
        return os.path.join(
            qbook_path,
            config.BASE_METADATA_PATH,
            migration_name,
            schema_name,
            object_type.upper(),
            object_name,
            config.LOG_SUBDIRECTORY
        )
    
    @staticmethod
    def _sanitize_filename_part(name: str) -> str:
        """
        Sanitize a filename part to be filesystem-safe.
        
        Args:
            name: Original name
            
        Returns:
            str: Sanitized name safe for filenames
        """
        if not name:
            return "unknown"
        
        # Replace problematic characters with underscores
        problematic_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|', ' ']
        sanitized = name
        
        for char in problematic_chars:
            sanitized = sanitized.replace(char, '_')
        
        # Remove multiple consecutive underscores
        while '__' in sanitized:
            sanitized = sanitized.replace('__', '_')
        
        # Remove leading/trailing underscores
        sanitized = sanitized.strip('_')
        
        # Ensure not empty
        if not sanitized:
            sanitized = "unknown"
        
        return sanitized
    
    @staticmethod
    def get_log_header(
        migration_name: str,
        schema_name: str,
        object_name: str,
        object_type: str,
        log_filename: str
    ) -> List[str]:
        """
        Generate standard log file header.
        
        Args:
            migration_name: Migration name
            schema_name: Schema name
            object_name: Object name
            object_type: Object type
            log_filename: Log filename
            
        Returns:
            List[str]: Header lines
        """
        from datetime import datetime
        
        config = LoggingConfig()
        
        header_lines = [
            "Stage2 Workflow Execution Log",
            config.SECTION_SEPARATOR,
            f"Migration: {migration_name}",
            f"Schema: {schema_name}",
            f"Object: {object_type}.{object_name}",
            f"Log File: {log_filename}",
            f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            config.SECTION_SEPARATOR,
            ""
        ]
        
        return header_lines


class LoggingPatterns:
    """
    Standard logging patterns and templates for consistent formatting.
    """
    
    # Workflow section patterns
    WORKFLOW_START = "STAGE2 WORKFLOW STARTED"
    WORKFLOW_END = "STAGE2 WORKFLOW COMPLETED"
    NODE_ENTRY = "NODE: {node_name} - STARTED"
    NODE_EXIT = "NODE: {node_name} - COMPLETED"
    
    # Processing patterns
    STATEMENT_START = "PROCESSING STATEMENT {statement_number} OF {total_statements}"
    STATEMENT_END = "STATEMENT {statement_number} COMPLETED - STATUS: {status}"
    
    # AI interaction patterns
    AI_REQUEST_START = "AI REQUEST: {operation_type}"
    AI_REQUEST_END = "AI RESPONSE: {operation_type} - STATUS: {status}"
    
    # Module execution patterns
    MODULE_EXECUTION_START = "MODULE EXECUTION: {module_name}"
    MODULE_EXECUTION_END = "MODULE EXECUTION: {module_name} - STATUS: {status}"
    
    # Error patterns
    ERROR_OCCURRED = "ERROR: {error_type} - {error_message}"
    WARNING_OCCURRED = "WARNING: {warning_message}"
    
    @staticmethod
    def format_pattern(pattern: str, **kwargs) -> str:
        """
        Format a logging pattern with provided arguments.
        
        Args:
            pattern: Pattern string with placeholders
            **kwargs: Values to substitute
            
        Returns:
            str: Formatted pattern
        """
        try:
            return pattern.format(**kwargs)
        except KeyError as e:
            return f"LOGGING_ERROR: Missing parameter {e} for pattern: {pattern}"
