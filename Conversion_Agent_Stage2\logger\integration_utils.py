"""
Integration utilities for Stage2 logging.

Provides helper functions to easily integrate logging into existing Stage2 workflow
without major code restructuring.
"""

import os
import sys
from typing import Optional, Dict, Any
from datetime import datetime

from .stage2_logger import setup_workflow_logging, log_and_print, log_section, log_subsection
from .logging_config import LoggingPatterns


class WorkflowLogger:
    """
    High-level workflow logger that provides convenient methods for common logging patterns.
    """
    
    def __init__(self):
        self.current_node = None
        self.current_statement = None
        self.workflow_started = False
    
    def initialize_workflow_logging(
        self,
        migration_name: str,
        schema_name: str,
        object_name: str,
        object_type: str = "TABLE",
        cloud_category: str = "local"
    ) -> str:
        """
        Initialize logging for the entire workflow.
        
        Args:
            migration_name: Migration name
            schema_name: Schema name
            object_name: Object name
            object_type: Object type
            cloud_category: Cloud category
            
        Returns:
            str: Log file path
        """
        log_file_path = setup_workflow_logging(
            migration_name=migration_name,
            schema_name=schema_name,
            object_name=object_name,
            object_type=object_type,
            cloud_category=cloud_category
        )
        
        # Log workflow start
        self.start_workflow()
        
        return log_file_path
    
    def start_workflow(self):
        """Log workflow start."""
        if not self.workflow_started:
            log_section(LoggingPatterns.WORKFLOW_START)
            self.workflow_started = True
    
    def end_workflow(self, status: str = "SUCCESS"):
        """Log workflow end."""
        log_section(LoggingPatterns.WORKFLOW_END + f" - STATUS: {status}")
    
    def start_node(self, node_name: str):
        """Log node start."""
        self.current_node = node_name
        log_section(LoggingPatterns.format_pattern(
            LoggingPatterns.NODE_ENTRY,
            node_name=node_name
        ))
    
    def end_node(self, node_name: str, status: str = "SUCCESS"):
        """Log node end."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.NODE_EXIT,
            node_name=node_name
        ) + f" - STATUS: {status}")
        log_and_print("")  # Add spacing
    
    def start_statement_processing(self, statement_number: int, total_statements: int):
        """Log statement processing start."""
        self.current_statement = statement_number
        log_subsection(LoggingPatterns.format_pattern(
            LoggingPatterns.STATEMENT_START,
            statement_number=statement_number,
            total_statements=total_statements
        ))
    
    def end_statement_processing(self, statement_number: int, status: str = "SUCCESS"):
        """Log statement processing end."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.STATEMENT_END,
            statement_number=statement_number,
            status=status
        ))
    
    def log_ai_request(self, operation_type: str, context: Optional[Dict[str, Any]] = None):
        """Log AI request start."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.AI_REQUEST_START,
            operation_type=operation_type
        ))
        
        if context:
            for key, value in context.items():
                log_and_print(f"   {key}: {value}")
    
    def log_ai_response(self, operation_type: str, status: str = "SUCCESS", result: Optional[Dict[str, Any]] = None):
        """Log AI response."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.AI_REQUEST_END,
            operation_type=operation_type,
            status=status
        ))
        
        if result:
            for key, value in result.items():
                log_and_print(f"   {key}: {value}")
    
    def log_module_execution(self, module_name: str, input_data: Optional[str] = None, output_data: Optional[str] = None, status: str = "SUCCESS"):
        """Log module execution."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.MODULE_EXECUTION_START,
            module_name=module_name
        ))
        
        if input_data:
            log_and_print(f"   Input Length: {len(input_data)} characters")
        
        if output_data:
            log_and_print(f"   Output Length: {len(output_data)} characters")
            log_and_print(f"   Content Changed: {input_data != output_data if input_data else 'Unknown'}")
        
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.MODULE_EXECUTION_END,
            module_name=module_name,
            status=status
        ))
    
    def log_error(self, error_type: str, error_message: str, context: Optional[Dict[str, Any]] = None):
        """Log error with context."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.ERROR_OCCURRED,
            error_type=error_type,
            error_message=error_message
        ))
        
        if context:
            log_and_print("Error Context:")
            for key, value in context.items():
                log_and_print(f"   {key}: {value}")
    
    def log_warning(self, warning_message: str):
        """Log warning."""
        log_and_print(LoggingPatterns.format_pattern(
            LoggingPatterns.WARNING_OCCURRED,
            warning_message=warning_message
        ))


# Global workflow logger instance
_workflow_logger: Optional[WorkflowLogger] = None


def get_workflow_logger() -> WorkflowLogger:
    """
    Get or create global workflow logger instance.
    
    Returns:
        WorkflowLogger: Global workflow logger
    """
    global _workflow_logger
    
    if _workflow_logger is None:
        _workflow_logger = WorkflowLogger()
    
    return _workflow_logger


def initialize_stage2_logging(
    migration_name: str,
    schema_name: str,
    object_name: str,
    object_type: str = "TABLE",
    cloud_category: str = "local"
) -> str:
    """
    Convenience function to initialize Stage2 logging.
    
    Args:
        migration_name: Migration name
        schema_name: Schema name
        object_name: Object name
        object_type: Object type
        cloud_category: Cloud category
        
    Returns:
        str: Log file path
    """
    logger = get_workflow_logger()
    return logger.initialize_workflow_logging(
        migration_name=migration_name,
        schema_name=schema_name,
        object_name=object_name,
        object_type=object_type,
        cloud_category=cloud_category
    )


# Convenience functions for common logging operations
def log_workflow_start():
    """Log workflow start."""
    get_workflow_logger().start_workflow()


def log_workflow_end(status: str = "SUCCESS"):
    """Log workflow end."""
    get_workflow_logger().end_workflow(status)


def log_node_start(node_name: str):
    """Log node start."""
    get_workflow_logger().start_node(node_name)


def log_node_end(node_name: str, status: str = "SUCCESS"):
    """Log node end."""
    get_workflow_logger().end_node(node_name, status)


def log_statement_start(statement_number: int, total_statements: int):
    """Log statement processing start."""
    get_workflow_logger().start_statement_processing(statement_number, total_statements)


def log_statement_end(statement_number: int, status: str = "SUCCESS"):
    """Log statement processing end."""
    get_workflow_logger().end_statement_processing(statement_number, status)


def log_ai_interaction(operation_type: str, status: str = "SUCCESS", context: Optional[Dict[str, Any]] = None, result: Optional[Dict[str, Any]] = None):
    """Log AI interaction."""
    logger = get_workflow_logger()
    logger.log_ai_request(operation_type, context)
    logger.log_ai_response(operation_type, status, result)


def log_module_execution_simple(module_name: str, input_length: int = 0, output_length: int = 0, status: str = "SUCCESS"):
    """Log module execution with simple parameters."""
    logger = get_workflow_logger()
    log_and_print(f"🔧 Module {module_name}: input_length={input_length}, output_length={output_length}, status={status}")


def log_error_simple(error_message: str, context: Optional[Dict[str, Any]] = None):
    """Log error with simple interface."""
    get_workflow_logger().log_error("EXECUTION_ERROR", error_message, context)


def log_warning_simple(warning_message: str):
    """Log warning with simple interface."""
    get_workflow_logger().log_warning(warning_message)
