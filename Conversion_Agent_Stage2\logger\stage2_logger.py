"""
Simple Stage2 Logger - Just captures print() to file
"""

import os
from datetime import datetime

# Global log file path
log_file_path = None

def setup_workflow_logging(migration_name, schema_name, object_name, object_type="TABLE", cloud_category="local"):
    """Setup log file for workflow"""
    global log_file_path

    try:
        from config import Config

        # Get base path
        if cloud_category.lower() == "local":
            qbook_path = Config.Qbook_Local_Path
        else:
            qbook_path = Config.Qbook_Path

        # Create log directory
        log_dir = os.path.join(
            qbook_path,
            "Stage1_Metadata",
            migration_name,
            schema_name,
            object_type,
            object_name,
            "logs"
        )
        os.makedirs(log_dir, exist_ok=True)

        log_filename = f"{schema_name}_{object_name}.log"
        log_file_path = os.path.join(log_dir, log_filename)

        # Write header
        with open(log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"Stage2 Workflow Log - {migration_name}/{schema_name}/{object_name}\n")
            f.write("=" * 80 + "\n\n")

        return log_file_path

    except Exception as e:
        print(f"Warning: Could not setup logging: {e}")
        return None


def log_and_print(message):
    """Print to console AND write to log file"""
    # Always print to console
    print(message)

    # Write to file if available
    global log_file_path
    if log_file_path:
        try:
            with open(log_file_path, 'a', encoding='utf-8') as f:
                f.write(str(message) + '\n')
        except:
            pass  # Ignore file errors
