"""
Stage2 Logger Implementation

Professional logging system for Stage2 workflow with best practices:
- Standardized file naming conventions
- Proper directory structure
- Console + file dual output
- Error handling and recovery
"""

import os
import sys
from datetime import datetime
from typing import Optional
from pathlib import Path


class Stage2Logger:
    """
    Professional Stage2 workflow logger with dual output (console + file).
    
    Features:
    - Standardized file naming: stage2_YYYYMMDD_HHMMSS_schema_object.log
    - Automatic directory creation
    - Error handling for file operations
    - Thread-safe file writing
    - Proper encoding handling
    """
    
    def __init__(self, log_file_path: str):
        """
        Initialize logger with specified log file path.
        
        Args:
            log_file_path: Full path to the log file
        """
        self.log_file_path = log_file_path
        self.is_initialized = False
        self._initialize_log_file()
    
    def _initialize_log_file(self):
        """Initialize log file and directory structure."""
        try:
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(self.log_file_path)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            # Create log file with header
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.write(f"Stage2 Workflow Log\n")
                f.write(f"Log File: {os.path.basename(self.log_file_path)}\n")
                f.write(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
            
            self.is_initialized = True
            
        except Exception as e:
            print(f"Warning: Could not initialize log file {self.log_file_path}: {e}")
            self.is_initialized = False
    
    def log(self, message: str):
        """
        Write message to both console and log file.
        
        Args:
            message: Message to log
        """
        # Always print to console (existing behavior)
        print(message)
        
        # Write to file if initialized
        if self.is_initialized:
            try:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(str(message) + '\n')
            except Exception as e:
                # Only print warning once to avoid spam
                if hasattr(self, '_file_error_shown'):
                    return
                print(f"Warning: Could not write to log file: {e}")
                self._file_error_shown = True
    
    def log_section(self, title: str):
        """
        Log a section header with proper formatting.
        
        Args:
            title: Section title
        """
        separator = "=" * 80
        self.log(separator)
        self.log(title)
        self.log(separator)
    
    def log_subsection(self, title: str):
        """
        Log a subsection header.
        
        Args:
            title: Subsection title
        """
        self.log(f"\n--- {title} ---")
    
    def get_log_file_path(self) -> str:
        """Get the current log file path."""
        return self.log_file_path


# Global logger instance
_global_logger: Optional[Stage2Logger] = None


def setup_workflow_logging(
    migration_name: str,
    schema_name: str, 
    object_name: str,
    object_type: str = "TABLE",
    cloud_category: str = "local"
) -> str:
    """
    Setup logging for Stage2 workflow with standardized naming conventions.
    
    File Naming Convention:
    stage2_YYYYMMDD_HHMMSS_migration_schema_objecttype_object.log
    
    Directory Structure:
    {qbook_path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/logs/
    
    Args:
        migration_name: Migration name (e.g., Oracle_Postgres14)
        schema_name: Schema name (e.g., HR)
        object_name: Object name (e.g., EMPLOYEES)
        object_type: Object type (e.g., TABLE, VIEW, PROCEDURE)
        cloud_category: Cloud category (local/cloud)
    
    Returns:
        str: Path to the created log file
    """
    global _global_logger
    
    try:
        # Import config here to avoid circular imports
        from config import Config
        
        # Determine base path based on cloud category
        if cloud_category.lower() == "local":
            qbook_path = Config.Qbook_Local_Path
        else:
            qbook_path = Config.Qbook_Path
        
        # Create log directory path following Stage1_Metadata pattern
        log_dir = os.path.join(
            qbook_path,
            "Stage1_Metadata",
            migration_name,
            schema_name,
            object_type.upper(),
            object_name,
            "logs"
        )
        
        # Generate timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create standardized filename
        # Format: stage2_YYYYMMDD_HHMMSS_migration_schema_objecttype_object.log
        log_filename = f"stage2_{timestamp}_{migration_name}_{schema_name}_{object_type.lower()}_{object_name}.log"
        
        # Full log file path
        log_file_path = os.path.join(log_dir, log_filename)
        
        # Initialize global logger
        _global_logger = Stage2Logger(log_file_path)
        
        # Log initialization success
        _global_logger.log_section("STAGE2 WORKFLOW LOGGING INITIALIZED")
        _global_logger.log(f"Migration: {migration_name}")
        _global_logger.log(f"Schema: {schema_name}")
        _global_logger.log(f"Object: {object_type}.{object_name}")
        _global_logger.log(f"Cloud Category: {cloud_category}")
        _global_logger.log(f"Log File: {log_filename}")
        _global_logger.log(f"Log Directory: {log_dir}")
        _global_logger.log("")
        
        return log_file_path
        
    except Exception as e:
        print(f"Error setting up workflow logging: {e}")
        # Create fallback logger in temp directory
        fallback_path = os.path.join("tmp", f"stage2_fallback_{timestamp}.log")
        _global_logger = Stage2Logger(fallback_path)
        return fallback_path


def log_and_print(message: str):
    """
    Convenience function to log message using global logger.
    Falls back to print if logger not initialized.
    
    Args:
        message: Message to log
    """
    global _global_logger
    
    if _global_logger:
        _global_logger.log(message)
    else:
        # Fallback to console only if logger not setup
        print(message)


def get_current_log_file() -> Optional[str]:
    """
    Get the current log file path.
    
    Returns:
        str: Current log file path or None if not initialized
    """
    global _global_logger
    
    if _global_logger:
        return _global_logger.get_log_file_path()
    return None


def log_section(title: str):
    """Log a section header."""
    global _global_logger
    
    if _global_logger:
        _global_logger.log_section(title)
    else:
        print("=" * 80)
        print(title)
        print("=" * 80)


def log_subsection(title: str):
    """Log a subsection header."""
    global _global_logger
    
    if _global_logger:
        _global_logger.log_subsection(title)
    else:
        print(f"\n--- {title} ---")
